#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2048游戏 - 拟态风格 50x50 超大棋盘
支持中文界面，拟态设计风格
"""

import tkinter as tk
from tkinter import messagebox, font
import random
import math

class Game2048:
    def __init__(self):
        self.size = 40  # 40x40 棋盘
        self.board = [[0 for _ in range(self.size)] for _ in range(self.size)]
        self.score = 0
        self.best_score = 0
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("2048游戏 - 40x40超大棋盘")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口大小和位置 - 正方形窗口
        window_size = 900  # 正方形窗口
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_size) // 2
        y = (screen_height - window_size) // 2
        self.root.geometry(f"{window_size}x{window_size}+{x}+{y}")
        self.root.minsize(600, 600)
        
        # 拟态风格颜色配置
        self.colors = {
            'bg': '#e0e5ec',
            'shadow_dark': '#a3b1c6',
            'shadow_light': '#ffffff',
            'text_dark': '#4a5568',
            'text_light': '#718096',
            'accent': '#667eea',
            'success': '#48bb78',
            'warning': '#ed8936'
        }
        
        # 数字颜色配置
        self.number_colors = {
            0: {'bg': '#e0e5ec', 'fg': '#4a5568'},
            2: {'bg': '#f7fafc', 'fg': '#2d3748'},
            4: {'bg': '#edf2f7', 'fg': '#2d3748'},
            8: {'bg': '#e2e8f0', 'fg': '#2d3748'},
            16: {'bg': '#cbd5e0', 'fg': '#2d3748'},
            32: {'bg': '#a0aec0', 'fg': '#ffffff'},
            64: {'bg': '#718096', 'fg': '#ffffff'},
            128: {'bg': '#4a5568', 'fg': '#ffffff'},
            256: {'bg': '#2d3748', 'fg': '#ffffff'},
            512: {'bg': '#1a202c', 'fg': '#ffffff'},
            1024: {'bg': '#667eea', 'fg': '#ffffff'},
            2048: {'bg': '#48bb78', 'fg': '#ffffff'},
        }
        
        self.setup_ui()
        self.add_random_tile()
        self.add_random_tile()
        self.update_display()
        
    def setup_ui(self):
        """设置用户界面"""
        # 顶部信息栏 - 固定高度，只显示分数
        info_frame = tk.Frame(self.root, bg=self.colors['bg'], height=50)
        info_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        info_frame.pack_propagate(False)  # 防止框架收缩
        self.setup_info_bar(info_frame)

        # 游戏区域框架 - 填充剩余的所有空间
        game_frame = tk.Frame(self.root, bg=self.colors['bg'])
        game_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 创建游戏网格 - 直接填充整个游戏区域，不使用滚动条
        self.setup_game_grid(game_frame)

        # 绑定键盘事件
        self.root.bind('<Key>', self.on_key_press)
        self.root.focus_set()
        
    def setup_info_bar(self, parent):
        """设置信息栏"""
        # 标题和分数在同一行
        title_label = tk.Label(
            parent,
            text="2048 超大棋盘 (40x40)",
            font=('Microsoft YaHei', 16, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text_dark']
        )
        title_label.pack(side=tk.LEFT)

        # 新游戏按钮
        new_game_btn = tk.Button(
            parent,
            text="新游戏",
            font=('Microsoft YaHei', 10),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            command=self.new_game
        )
        new_game_btn.pack(side=tk.LEFT, padx=(20, 0))

        # 分数显示在右侧
        score_frame = tk.Frame(parent, bg=self.colors['bg'])
        score_frame.pack(side=tk.RIGHT)

        self.score_label = tk.Label(
            score_frame,
            text=f"分数: {self.score}",
            font=('Microsoft YaHei', 12),
            bg=self.colors['bg'],
            fg=self.colors['text_dark']
        )
        self.score_label.pack(side=tk.LEFT, padx=(0, 15))

        self.best_label = tk.Label(
            score_frame,
            text=f"最高: {self.best_score}",
            font=('Microsoft YaHei', 12),
            bg=self.colors['bg'],
            fg=self.colors['text_dark']
        )
        self.best_label.pack(side=tk.LEFT)
        
    def setup_game_grid(self, parent):
        """设置游戏网格 - 使用Canvas优化性能"""
        # 创建画布
        self.canvas = tk.Canvas(
            parent,
            bg=self.colors['bg'],
            highlightthickness=0
        )
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定窗口大小改变事件
        self.canvas.bind('<Configure>', self.on_canvas_configure)

        # 初始化格子数据
        self.cells = []
        self.cell_size = 10  # 初始格子大小
        self.gap = 1

        # 创建格子（延迟到窗口配置完成后）
        self.canvas.after(100, self.create_cells)

    def on_canvas_configure(self, event):
        """画布大小改变时重新计算格子大小"""
        canvas_width = event.width
        canvas_height = event.height

        # 计算最适合的格子大小
        available_width = canvas_width - (self.size + 1) * self.gap
        available_height = canvas_height - (self.size + 1) * self.gap

        cell_width = available_width // self.size
        cell_height = available_height // self.size

        # 使用较小的值确保格子是正方形
        new_cell_size = min(cell_width, cell_height, 15)  # 最大15像素（适应40x40）

        if new_cell_size != self.cell_size and new_cell_size > 2:
            self.cell_size = new_cell_size
            self.recreate_cells()

    def create_cells(self):
        """创建格子"""
        if hasattr(self, 'cells') and self.cells:
            return  # 避免重复创建

        self.cells = []
        for i in range(self.size):
            row = []
            for j in range(self.size):
                x1 = j * (self.cell_size + self.gap) + self.gap
                y1 = i * (self.cell_size + self.gap) + self.gap
                x2 = x1 + self.cell_size
                y2 = y1 + self.cell_size

                cell = self.canvas.create_rectangle(
                    x1, y1, x2, y2,
                    fill=self.colors['bg'],
                    outline=self.colors['shadow_dark'],
                    width=1
                )

                text = self.canvas.create_text(
                    (x1 + x2) / 2, (y1 + y2) / 2,
                    text="",
                    font=('Microsoft YaHei', max(6, self.cell_size // 3)),
                    fill=self.colors['text_dark']
                )

                row.append({'rect': cell, 'text': text})
            self.cells.append(row)

    def recreate_cells(self):
        """重新创建格子以适应新的大小"""
        # 清除现有格子
        if hasattr(self, 'cells'):
            for row in self.cells:
                for cell in row:
                    self.canvas.delete(cell['rect'])
                    self.canvas.delete(cell['text'])

        # 重新创建
        self.cells = []
        self.create_cells()
        self.update_display()

    def setup_game_canvas_old(self, parent):
        """设置游戏画布"""
        # 创建画布框架
        canvas_frame = tk.Frame(parent, bg=self.colors['bg'])
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建画布
        self.canvas = tk.Canvas(
            canvas_frame,
            bg=self.colors['bg'],
            highlightthickness=0
        )
        
        # 创建滚动条
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建游戏网格 - 缩小格子以适应50x50棋盘
        self.cell_size = 12  # 每个格子的大小（缩小）
        self.gap = 1  # 格子间隙（缩小）
        
        canvas_width = self.size * (self.cell_size + self.gap) + self.gap
        canvas_height = self.size * (self.cell_size + self.gap) + self.gap
        
        self.canvas.configure(scrollregion=(0, 0, canvas_width, canvas_height))
        
        # 创建格子
        self.cells = []
        for i in range(self.size):
            row = []
            for j in range(self.size):
                x1 = j * (self.cell_size + self.gap) + self.gap
                y1 = i * (self.cell_size + self.gap) + self.gap
                x2 = x1 + self.cell_size
                y2 = y1 + self.cell_size
                
                cell = self.canvas.create_rectangle(
                    x1, y1, x2, y2,
                    fill=self.colors['bg'],
                    outline=self.colors['shadow_dark'],
                    width=1
                )
                
                text = self.canvas.create_text(
                    (x1 + x2) / 2, (y1 + y2) / 2,
                    text="",
                    font=('Microsoft YaHei', 6),  # 缩小字体以适应小格子
                    fill=self.colors['text_dark']
                )
                
                row.append({'rect': cell, 'text': text})
            self.cells.append(row)
            

        
    def add_random_tile(self):
        """添加随机数字块"""
        empty_cells = []
        for i in range(self.size):
            for j in range(self.size):
                if self.board[i][j] == 0:
                    empty_cells.append((i, j))
        
        if empty_cells:
            i, j = random.choice(empty_cells)
            self.board[i][j] = 2 if random.random() < 0.9 else 4
            
    def update_display(self):
        """更新显示 - 优化性能，只更新有变化的格子"""
        if not hasattr(self, 'cells') or not self.cells:
            return

        # 批量更新，减少重绘次数
        updates = []

        for i in range(self.size):
            for j in range(self.size):
                value = self.board[i][j]

                # 跳过没有变化的格子（可以进一步优化）
                if hasattr(self, 'last_board') and self.last_board[i][j] == value:
                    continue

                cell = self.cells[i][j]

                # 更新颜色
                if value in self.number_colors:
                    colors = self.number_colors[value]
                else:
                    colors = self.number_colors[2048]

                # 收集更新操作
                updates.append((cell['rect'], 'fill', colors['bg']))

                # 更新文字
                text = str(value) if value > 0 else ""
                updates.append((cell['text'], 'text', text))
                updates.append((cell['text'], 'fill', colors['fg']))

        # 批量执行更新
        for item, option, value in updates:
            self.canvas.itemconfig(item, **{option: value})

        # 保存当前状态用于下次比较
        self.last_board = [row[:] for row in self.board]

        # 更新分数
        self.score_label.config(text=f"分数: {self.score}")
        if self.score > self.best_score:
            self.best_score = self.score
        self.best_label.config(text=f"最高: {self.best_score}")

    def move_left(self):
        """向左移动"""
        moved = False
        for i in range(self.size):
            # 移除零并合并
            row = [x for x in self.board[i] if x != 0]

            # 合并相同数字
            j = 0
            while j < len(row) - 1:
                if row[j] == row[j + 1]:
                    row[j] *= 2
                    self.score += row[j]
                    row.pop(j + 1)
                j += 1

            # 补充零
            while len(row) < self.size:
                row.append(0)

            # 检查是否有变化
            if row != self.board[i]:
                moved = True
                self.board[i] = row

        return moved

    def move_right(self):
        """向右移动"""
        moved = False
        for i in range(self.size):
            # 反转行，向左移动，再反转回来
            self.board[i] = self.board[i][::-1]

        if self.move_left():
            moved = True

        for i in range(self.size):
            self.board[i] = self.board[i][::-1]

        return moved

    def move_up(self):
        """向上移动"""
        # 转置矩阵
        self.transpose()
        moved = self.move_left()
        self.transpose()
        return moved

    def move_down(self):
        """向下移动"""
        # 转置矩阵
        self.transpose()
        moved = self.move_right()
        self.transpose()
        return moved

    def transpose(self):
        """转置矩阵"""
        self.board = [[self.board[j][i] for j in range(self.size)] for i in range(self.size)]

    def can_move(self):
        """检查是否还能移动"""
        # 检查是否有空格
        for i in range(self.size):
            for j in range(self.size):
                if self.board[i][j] == 0:
                    return True

        # 检查是否有相邻相同数字
        for i in range(self.size):
            for j in range(self.size):
                current = self.board[i][j]
                # 检查右边
                if j < self.size - 1 and self.board[i][j + 1] == current:
                    return True
                # 检查下面
                if i < self.size - 1 and self.board[i + 1][j] == current:
                    return True

        return False

    def check_win(self):
        """检查是否获胜"""
        for i in range(self.size):
            for j in range(self.size):
                if self.board[i][j] >= 2048:
                    return True
        return False

    def on_key_press(self, event):
        """处理键盘事件"""
        key = event.keysym.lower()
        moved = False

        if key in ['left', 'a']:
            moved = self.move_left()
        elif key in ['right', 'd']:
            moved = self.move_right()
        elif key in ['up', 'w']:
            moved = self.move_up()
        elif key in ['down', 's']:
            moved = self.move_down()

        if moved:
            self.add_random_tile()
            self.update_display()

            # 检查游戏状态
            if self.check_win():
                messagebox.showinfo("恭喜！", "你达到了2048！游戏胜利！")
            elif not self.can_move():
                messagebox.showinfo("游戏结束", f"无法继续移动！\n最终分数: {self.score}")

    def new_game(self):
        """开始新游戏"""
        # 只重置游戏数据，不重新创建界面
        self.board = [[0 for _ in range(self.size)] for _ in range(self.size)]
        self.score = 0

        # 重置上次状态
        if hasattr(self, 'last_board'):
            del self.last_board

        # 添加初始数字块
        self.add_random_tile()
        self.add_random_tile()
        self.update_display()

    def run(self):
        """运行游戏"""
        self.root.mainloop()

def main():
    """主函数"""
    game = Game2048()
    game.run()

if __name__ == "__main__":
    main()
