#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2048游戏 - 拟态风格 50x50 超大棋盘
支持中文界面，拟态设计风格
"""

import tkinter as tk
from tkinter import messagebox, font
import random
import math

class Game2048:
    def __init__(self):
        self.size = 50  # 50x50 棋盘
        self.board = [[0 for _ in range(self.size)] for _ in range(self.size)]
        self.score = 0
        self.best_score = 0
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("2048游戏 - 50x50超大棋盘")
        self.root.configure(bg='#f0f0f0')
        
        # 设置窗口大小和位置
        window_width = 1200
        window_height = 800
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(800, 600)
        
        # 拟态风格颜色配置
        self.colors = {
            'bg': '#e0e5ec',
            'shadow_dark': '#a3b1c6',
            'shadow_light': '#ffffff',
            'text_dark': '#4a5568',
            'text_light': '#718096',
            'accent': '#667eea',
            'success': '#48bb78',
            'warning': '#ed8936'
        }
        
        # 数字颜色配置
        self.number_colors = {
            0: {'bg': '#e0e5ec', 'fg': '#4a5568'},
            2: {'bg': '#f7fafc', 'fg': '#2d3748'},
            4: {'bg': '#edf2f7', 'fg': '#2d3748'},
            8: {'bg': '#e2e8f0', 'fg': '#2d3748'},
            16: {'bg': '#cbd5e0', 'fg': '#2d3748'},
            32: {'bg': '#a0aec0', 'fg': '#ffffff'},
            64: {'bg': '#718096', 'fg': '#ffffff'},
            128: {'bg': '#4a5568', 'fg': '#ffffff'},
            256: {'bg': '#2d3748', 'fg': '#ffffff'},
            512: {'bg': '#1a202c', 'fg': '#ffffff'},
            1024: {'bg': '#667eea', 'fg': '#ffffff'},
            2048: {'bg': '#48bb78', 'fg': '#ffffff'},
        }
        
        self.setup_ui()
        self.add_random_tile()
        self.add_random_tile()
        self.update_display()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg=self.colors['bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 顶部信息栏
        self.setup_info_bar(main_frame)
        
        # 游戏区域框架
        game_frame = tk.Frame(main_frame, bg=self.colors['bg'])
        game_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # 创建画布和滚动条
        self.setup_game_canvas(game_frame)
        
        # 底部控制栏
        self.setup_control_bar(main_frame)
        
        # 绑定键盘事件
        self.root.bind('<Key>', self.on_key_press)
        self.root.focus_set()
        
    def setup_info_bar(self, parent):
        """设置信息栏"""
        info_frame = tk.Frame(parent, bg=self.colors['bg'])
        info_frame.pack(fill=tk.X)
        
        # 标题
        title_label = tk.Label(
            info_frame, 
            text="2048 超大棋盘", 
            font=('Microsoft YaHei', 24, 'bold'),
            bg=self.colors['bg'],
            fg=self.colors['text_dark']
        )
        title_label.pack(side=tk.LEFT)
        
        # 分数显示
        score_frame = tk.Frame(info_frame, bg=self.colors['bg'])
        score_frame.pack(side=tk.RIGHT)
        
        self.score_label = tk.Label(
            score_frame,
            text=f"分数: {self.score}",
            font=('Microsoft YaHei', 16),
            bg=self.colors['bg'],
            fg=self.colors['text_dark']
        )
        self.score_label.pack(side=tk.LEFT, padx=(0, 20))
        
        self.best_label = tk.Label(
            score_frame,
            text=f"最高: {self.best_score}",
            font=('Microsoft YaHei', 16),
            bg=self.colors['bg'],
            fg=self.colors['text_dark']
        )
        self.best_label.pack(side=tk.LEFT)
        
    def setup_game_canvas(self, parent):
        """设置游戏画布"""
        # 创建画布框架
        canvas_frame = tk.Frame(parent, bg=self.colors['bg'])
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建画布
        self.canvas = tk.Canvas(
            canvas_frame,
            bg=self.colors['bg'],
            highlightthickness=0
        )
        
        # 创建滚动条
        v_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建游戏网格
        self.cell_size = 20  # 每个格子的大小
        self.gap = 2  # 格子间隙
        
        canvas_width = self.size * (self.cell_size + self.gap) + self.gap
        canvas_height = self.size * (self.cell_size + self.gap) + self.gap
        
        self.canvas.configure(scrollregion=(0, 0, canvas_width, canvas_height))
        
        # 创建格子
        self.cells = []
        for i in range(self.size):
            row = []
            for j in range(self.size):
                x1 = j * (self.cell_size + self.gap) + self.gap
                y1 = i * (self.cell_size + self.gap) + self.gap
                x2 = x1 + self.cell_size
                y2 = y1 + self.cell_size
                
                cell = self.canvas.create_rectangle(
                    x1, y1, x2, y2,
                    fill=self.colors['bg'],
                    outline=self.colors['shadow_dark'],
                    width=1
                )
                
                text = self.canvas.create_text(
                    (x1 + x2) / 2, (y1 + y2) / 2,
                    text="",
                    font=('Microsoft YaHei', 8),
                    fill=self.colors['text_dark']
                )
                
                row.append({'rect': cell, 'text': text})
            self.cells.append(row)
            
    def setup_control_bar(self, parent):
        """设置控制栏"""
        control_frame = tk.Frame(parent, bg=self.colors['bg'])
        control_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 新游戏按钮
        new_game_btn = tk.Button(
            control_frame,
            text="新游戏",
            font=('Microsoft YaHei', 12),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            command=self.new_game
        )
        new_game_btn.pack(side=tk.LEFT)
        
        # 操作说明
        instruction_label = tk.Label(
            control_frame,
            text="使用方向键或WASD移动方块",
            font=('Microsoft YaHei', 10),
            bg=self.colors['bg'],
            fg=self.colors['text_light']
        )
        instruction_label.pack(side=tk.RIGHT)
        
    def add_random_tile(self):
        """添加随机数字块"""
        empty_cells = []
        for i in range(self.size):
            for j in range(self.size):
                if self.board[i][j] == 0:
                    empty_cells.append((i, j))
        
        if empty_cells:
            i, j = random.choice(empty_cells)
            self.board[i][j] = 2 if random.random() < 0.9 else 4
            
    def update_display(self):
        """更新显示"""
        for i in range(self.size):
            for j in range(self.size):
                value = self.board[i][j]
                cell = self.cells[i][j]
                
                # 更新颜色
                if value in self.number_colors:
                    colors = self.number_colors[value]
                else:
                    colors = self.number_colors[2048]
                
                self.canvas.itemconfig(cell['rect'], fill=colors['bg'])
                
                # 更新文字
                text = str(value) if value > 0 else ""
                self.canvas.itemconfig(cell['text'], text=text, fill=colors['fg'])
        
        # 更新分数
        self.score_label.config(text=f"分数: {self.score}")
        if self.score > self.best_score:
            self.best_score = self.score
        self.best_label.config(text=f"最高: {self.best_score}")

    def move_left(self):
        """向左移动"""
        moved = False
        for i in range(self.size):
            # 移除零并合并
            row = [x for x in self.board[i] if x != 0]

            # 合并相同数字
            j = 0
            while j < len(row) - 1:
                if row[j] == row[j + 1]:
                    row[j] *= 2
                    self.score += row[j]
                    row.pop(j + 1)
                j += 1

            # 补充零
            while len(row) < self.size:
                row.append(0)

            # 检查是否有变化
            if row != self.board[i]:
                moved = True
                self.board[i] = row

        return moved

    def move_right(self):
        """向右移动"""
        moved = False
        for i in range(self.size):
            # 反转行，向左移动，再反转回来
            self.board[i] = self.board[i][::-1]

        if self.move_left():
            moved = True

        for i in range(self.size):
            self.board[i] = self.board[i][::-1]

        return moved

    def move_up(self):
        """向上移动"""
        # 转置矩阵
        self.transpose()
        moved = self.move_left()
        self.transpose()
        return moved

    def move_down(self):
        """向下移动"""
        # 转置矩阵
        self.transpose()
        moved = self.move_right()
        self.transpose()
        return moved

    def transpose(self):
        """转置矩阵"""
        self.board = [[self.board[j][i] for j in range(self.size)] for i in range(self.size)]

    def can_move(self):
        """检查是否还能移动"""
        # 检查是否有空格
        for i in range(self.size):
            for j in range(self.size):
                if self.board[i][j] == 0:
                    return True

        # 检查是否有相邻相同数字
        for i in range(self.size):
            for j in range(self.size):
                current = self.board[i][j]
                # 检查右边
                if j < self.size - 1 and self.board[i][j + 1] == current:
                    return True
                # 检查下面
                if i < self.size - 1 and self.board[i + 1][j] == current:
                    return True

        return False

    def check_win(self):
        """检查是否获胜"""
        for i in range(self.size):
            for j in range(self.size):
                if self.board[i][j] >= 2048:
                    return True
        return False

    def on_key_press(self, event):
        """处理键盘事件"""
        key = event.keysym.lower()
        moved = False

        if key in ['left', 'a']:
            moved = self.move_left()
        elif key in ['right', 'd']:
            moved = self.move_right()
        elif key in ['up', 'w']:
            moved = self.move_up()
        elif key in ['down', 's']:
            moved = self.move_down()

        if moved:
            self.add_random_tile()
            self.update_display()

            # 检查游戏状态
            if self.check_win():
                messagebox.showinfo("恭喜！", "你达到了2048！游戏胜利！")
            elif not self.can_move():
                messagebox.showinfo("游戏结束", f"无法继续移动！\n最终分数: {self.score}")

    def new_game(self):
        """开始新游戏"""
        self.board = [[0 for _ in range(self.size)] for _ in range(self.size)]
        self.score = 0
        self.add_random_tile()
        self.add_random_tile()
        self.update_display()

    def run(self):
        """运行游戏"""
        self.root.mainloop()

def main():
    """主函数"""
    game = Game2048()
    game.run()

if __name__ == "__main__":
    main()
